import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends User {
  const UserModel({
    required super.uid,
    required super.name,
    required super.email,
    required super.phone,
    required super.location,
    required super.userRole,
    required super.status,
    required super.imageUrl,
    super.verificationStatus,
    super.credits,
    super.recentlyViewed,
    super.specialization,
    super.languages,
    super.rating,
    super.experience,
    super.propertiesSold,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromEntity(User user) {
    return UserModel(
      uid: user.uid,
      name: user.name,
      email: user.email,
      phone: user.phone,
      location: user.location,
      userRole: user.userRole,
      status: user.status,
      imageUrl: user.imageUrl,
      verificationStatus: user.verificationStatus,
      credits: user.credits,
      recentlyViewed: user.recentlyViewed,
      specialization: user.specialization,
      languages: user.languages,
      rating: user.rating,
      experience: user.experience,
      propertiesSold: user.propertiesSold,
    );
  }

  User toEntity() {
    return User(
      uid: uid,
      name: name,
      email: email,
      phone: phone,
      location: location,
      userRole: userRole,
      status: status,
      imageUrl: imageUrl,
      verificationStatus: verificationStatus,
      credits: credits,
      recentlyViewed: recentlyViewed,
      specialization: specialization,
      languages: languages,
      rating: rating,
      experience: experience,
      propertiesSold: propertiesSold,
    );
  }
}
