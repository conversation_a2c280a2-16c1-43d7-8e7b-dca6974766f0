import '../../domain/entities/agent.dart';
import '../../../auth/domain/entities/user.dart';

// part 'agent_model.g.dart'; // Uncomment after running build_runner

// @JsonSerializable() // Uncomment after running build_runner
class AgentModel extends Agent {
  const AgentModel({
    required super.agentId,
    required super.name,
    required super.email,
    required super.phone,
    required super.location,
    required super.verificationStatus,
    required super.imageUrl,
    required super.specialization,
    required super.languages,
    required super.rating,
    required super.experience,
    required super.propertiesSold,
    super.credits,
    super.bio,
    super.company,
    super.licenseNumber,
    super.joinedDate,
  });

  factory AgentModel.fromJson(Map<String, dynamic> json) => _$AgentModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$AgentModelToJson(this);

  factory AgentModel.fromEntity(Agent agent) {
    return AgentModel(
      agentId: agent.agentId,
      name: agent.name,
      email: agent.email,
      phone: agent.phone,
      location: agent.location,
      verificationStatus: agent.verificationStatus,
      imageUrl: agent.imageUrl,
      specialization: agent.specialization,
      languages: agent.languages,
      rating: agent.rating,
      experience: agent.experience,
      propertiesSold: agent.propertiesSold,
      credits: agent.credits,
      bio: agent.bio,
      company: agent.company,
      licenseNumber: agent.licenseNumber,
      joinedDate: agent.joinedDate,
    );
  }

  Agent toEntity() {
    return Agent(
      agentId: agentId,
      name: name,
      email: email,
      phone: phone,
      location: location,
      verificationStatus: verificationStatus,
      imageUrl: imageUrl,
      specialization: specialization,
      languages: languages,
      rating: rating,
      experience: experience,
      propertiesSold: propertiesSold,
      credits: credits,
      bio: bio,
      company: company,
      licenseNumber: licenseNumber,
      joinedDate: joinedDate,
    );
  }
}
