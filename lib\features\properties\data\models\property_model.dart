import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/property.dart';
import '../../../auth/domain/entities/user.dart';

part 'property_model.g.dart';

@JsonSerializable()
class PropertyModel extends Property {
  const PropertyModel({
    required super.propertyId,
    required super.address,
    required super.type,
    required super.status,
    required super.price,
    required super.bedrooms,
    required super.bathrooms,
    required super.sqft,
    required super.amenities,
    required super.predictedValueGrowth,
    required super.investmentPotentialScore,
    required super.imageUrls,
    required super.latitude,
    required super.longitude,
    required super.summary,
    required super.verificationStatus,
    required super.ownerId,
    required super.isBoosted,
    required super.views,
    required super.inquiries,
    required super.valueForMoneyScore,
    required super.demandScore,
    super.videoUrl,
    super.soldDate,
    super.createdAt,
    super.updatedAt,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) => _$PropertyModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$PropertyModelToJson(this);

  factory PropertyModel.fromEntity(Property property) {
    return PropertyModel(
      propertyId: property.propertyId,
      address: property.address,
      type: property.type,
      status: property.status,
      price: property.price,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      sqft: property.sqft,
      amenities: property.amenities,
      predictedValueGrowth: property.predictedValueGrowth,
      investmentPotentialScore: property.investmentPotentialScore,
      imageUrls: property.imageUrls,
      latitude: property.latitude,
      longitude: property.longitude,
      summary: property.summary,
      verificationStatus: property.verificationStatus,
      videoUrl: property.videoUrl,
      ownerId: property.ownerId,
      isBoosted: property.isBoosted,
      views: property.views,
      inquiries: property.inquiries,
      soldDate: property.soldDate,
      valueForMoneyScore: property.valueForMoneyScore,
      demandScore: property.demandScore,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
    );
  }

  Property toEntity() {
    return Property(
      propertyId: propertyId,
      address: address,
      type: type,
      status: status,
      price: price,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      sqft: sqft,
      amenities: amenities,
      predictedValueGrowth: predictedValueGrowth,
      investmentPotentialScore: investmentPotentialScore,
      imageUrls: imageUrls,
      latitude: latitude,
      longitude: longitude,
      summary: summary,
      verificationStatus: verificationStatus,
      videoUrl: videoUrl,
      ownerId: ownerId,
      isBoosted: isBoosted,
      views: views,
      inquiries: inquiries,
      soldDate: soldDate,
      valueForMoneyScore: valueForMoneyScore,
      demandScore: demandScore,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
